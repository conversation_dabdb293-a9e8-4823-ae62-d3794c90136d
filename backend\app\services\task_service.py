import json
import redis.asyncio as redis
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from app.models.request_models import TranscribeRequest
from app.models.response_models import TaskStatus, TaskStatusResponse, FileInfo
from app.core.config import settings

class TaskService:
    """任务管理服务"""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.task_prefix = "task:"
        self.task_ttl = 86400  # 24小时过期
    
    async def create_task(
        self, 
        task_id: str, 
        file_info: FileInfo, 
        request_params: TranscribeRequest
    ) -> None:
        """创建新任务"""
        task_data = {
            "task_id": task_id,
            "status": TaskStatus.PENDING.value,
            "progress": 0,
            "message": "任务已创建，等待处理",
            "file_info": file_info.model_dump(),
            "request_params": request_params.model_dump(),
            "result": None,
            "error": None,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "completed_at": None,
            "processing_time": None
        }
        
        await self.redis_client.setex(
            f"{self.task_prefix}{task_id}",
            self.task_ttl,
            json.dumps(task_data, ensure_ascii=False)
        )
    
    async def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """获取任务状态"""
        task_data = await self.redis_client.get(f"{self.task_prefix}{task_id}")
        
        if not task_data:
            return None
        
        data = json.loads(task_data)
        
        return TaskStatusResponse(
            task_id=data["task_id"],
            status=TaskStatus(data["status"]),
            progress=data["progress"],
            message=data["message"],
            result=data["result"],
            error=data["error"],
            created_at=datetime.fromisoformat(data["created_at"]),
            completed_at=datetime.fromisoformat(data["completed_at"]) if data["completed_at"] else None,
            processing_time=data["processing_time"]
        )
    
    async def update_task_progress(self, task_id: str, progress: int, message: str) -> None:
        """更新任务进度"""
        task_data = await self.redis_client.get(f"{self.task_prefix}{task_id}")
        
        if task_data:
            data = json.loads(task_data)
            data["status"] = TaskStatus.PROCESSING.value
            data["progress"] = progress
            data["message"] = message
            
            await self.redis_client.setex(
                f"{self.task_prefix}{task_id}",
                self.task_ttl,
                json.dumps(data, ensure_ascii=False)
            )
    
    async def complete_task(
        self, 
        task_id: str, 
        result: Dict[str, Any], 
        processing_time: float
    ) -> None:
        """完成任务"""
        task_data = await self.redis_client.get(f"{self.task_prefix}{task_id}")
        
        if task_data:
            data = json.loads(task_data)
            data["status"] = TaskStatus.COMPLETED.value
            data["progress"] = 100
            data["message"] = "转录完成"
            data["result"] = result
            data["completed_at"] = datetime.now(timezone.utc).isoformat()
            data["processing_time"] = processing_time
            
            await self.redis_client.setex(
                f"{self.task_prefix}{task_id}",
                self.task_ttl,
                json.dumps(data, ensure_ascii=False)
            )
    
    async def fail_task(self, task_id: str, error_message: str) -> None:
        """标记任务失败"""
        task_data = await self.redis_client.get(f"{self.task_prefix}{task_id}")
        
        if task_data:
            data = json.loads(task_data)
            data["status"] = TaskStatus.FAILED.value
            data["progress"] = 0
            data["message"] = "转录失败"
            data["error"] = error_message
            data["completed_at"] = datetime.now(timezone.utc).isoformat()
            
            await self.redis_client.setex(
                f"{self.task_prefix}{task_id}",
                self.task_ttl,
                json.dumps(data, ensure_ascii=False)
            )
    
    async def calculate_processing_time(self, task_id: str) -> float:
        """计算处理时间"""
        task_data = await self.redis_client.get(f"{self.task_prefix}{task_id}")
        
        if task_data:
            data = json.loads(task_data)
            created_at = datetime.fromisoformat(data["created_at"])
            now = datetime.now(timezone.utc)
            return (now - created_at).total_seconds()
        
        return 0.0
    
    async def cleanup_expired_tasks(self) -> None:
        """清理过期任务（可选的定期清理任务）"""
        # Redis的TTL会自动处理过期，这里可以添加额外的清理逻辑
        pass
